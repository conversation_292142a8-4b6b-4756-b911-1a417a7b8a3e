from utils.paths import (
    get_home_dir, get_app_data_dir,
    get_editor_storage_path, get_editor_db_path,
    get_editor_machine_id_path, get_editor_workspace_storage_path
)
from utils.editor_detector import detect_installed_editors, get_available_editors, print_editor_status, validate_editor_choice
from augutils.json_modifier import modify_telemetry_ids
from augutils.sqlite_modifier import clean_augment_data
from augutils.workspace_cleaner import clean_workspace_storage


def display_menu():
    """Display the main menu options."""
    print("\n" + "="*60)
    print("🚀 FREE AUGMENTCODE - MULTI-EDITOR SUPPORT")
    print("="*60)
    print("Hỗ trợ làm sạch dữ liệu cho cả VS Code và Cursor AI")
    print("Cho phép đăng nhập nhiều tài khoản mà không bị khóa")
    print("="*60)


def choose_editor() -> str:
    """
    Let user choose which editor to clean.

    Returns:
        str: Selected editor name
    """
    print("\n📋 PHÁT HIỆN EDITOR ĐƯỢC CÀI ĐẶT:")
    print_editor_status()

    available_editors = get_available_editors()

    if not available_editors:
        print("\n❌ Không tìm thấy VS Code hoặc Cursor AI nào được cài đặt!")
        print("Vui lòng cài đặt ít nhất một trong hai editor trước khi sử dụng tool này.")
        return None

    print(f"\n✅ Tìm thấy {len(available_editors)} editor(s) có thể làm sạch:")

    # Create menu options
    menu_options = {}
    for i, editor in enumerate(available_editors, 1):
        editor_name = "VS Code" if editor == "vscode" else "Cursor AI"
        menu_options[str(i)] = editor
        print(f"  {i}. {editor_name}")

    # Add option to clean both
    if len(available_editors) > 1:
        menu_options[str(len(available_editors) + 1)] = "both"
        print(f"  {len(available_editors) + 1}. Làm sạch cả hai")

    print("  0. Thoát")

    while True:
        choice = input(f"\n🎯 Chọn editor cần làm sạch (0-{len(menu_options)}): ").strip()

        if choice == "0":
            print("👋 Tạm biệt!")
            return None

        if choice in menu_options:
            return menu_options[choice]

        print("❌ Lựa chọn không hợp lệ! Vui lòng thử lại.")


def clean_editor_data(editor: str) -> bool:
    """
    Clean data for a specific editor.

    Args:
        editor (str): Editor name to clean

    Returns:
        bool: True if successful, False otherwise
    """
    editor_name = "VS Code" if editor == "vscode" else "Cursor AI"
    print(f"\n🧹 BẮT ĐẦU LÀM SẠCH DỮ LIỆU CHO {editor_name.upper()}:")
    print("="*50)

    try:
        # Display paths
        print(f"📁 Đường dẫn {editor_name}:")
        print(f"  Storage: {get_editor_storage_path(editor)}")
        print(f"  Database: {get_editor_db_path(editor)}")
        print(f"  Machine ID: {get_editor_machine_id_path(editor)}")
        print(f"  Workspace: {get_editor_workspace_storage_path(editor)}")

        # Modify telemetry IDs
        print(f"\n🔄 Đang thay đổi Telemetry IDs cho {editor_name}...")
        result = modify_telemetry_ids(editor)

        print("✅ Backup được tạo tại:")
        print(f"  📄 Storage backup: {result['storage_backup_path']}")
        if result['machine_id_backup_path']:
            print(f"  🔑 Machine ID backup: {result['machine_id_backup_path']}")

        print("\n📊 ID cũ:")
        print(f"  Machine ID: {result['old_machine_id']}")
        print(f"  Device ID: {result['old_device_id']}")

        print("\n🆕 ID mới:")
        print(f"  Machine ID: {result['new_machine_id']}")
        print(f"  Device ID: {result['new_device_id']}")

        # Clean SQLite Database
        print(f"\n🗃️ Đang làm sạch SQLite Database cho {editor_name}...")
        db_result = clean_augment_data(editor)
        print(f"✅ Database backup: {db_result['db_backup_path']}")
        print(f"🗑️ Đã xóa {db_result['deleted_rows']} bản ghi chứa 'augment'")

        # Clean Workspace Storage
        print(f"\n📁 Đang làm sạch Workspace Storage cho {editor_name}...")
        ws_result = clean_workspace_storage(editor)
        print(f"✅ Workspace backup: {ws_result['backup_path']}")
        print(f"🗑️ Đã xóa {ws_result['deleted_files_count']} file từ workspace storage")

        print(f"\n🎉 HOÀN THÀNH LÀM SẠCH {editor_name.upper()}!")
        print(f"Bây giờ bạn có thể khởi động {editor_name} và đăng nhập với email mới.")

        return True

    except FileNotFoundError as e:
        print(f"❌ Lỗi: {e}")
        return False
    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        return False


def main():
    """Main function."""
    display_menu()

    # Choose editor
    selected_editor = choose_editor()
    if not selected_editor:
        return

    # Confirm action
    if selected_editor == "both":
        available_editors = get_available_editors()
        editor_names = [("VS Code" if e == "vscode" else "Cursor AI") for e in available_editors]
        confirm_msg = f"Bạn có chắc muốn làm sạch dữ liệu cho {' và '.join(editor_names)}?"
    else:
        editor_name = "VS Code" if selected_editor == "vscode" else "Cursor AI"
        confirm_msg = f"Bạn có chắc muốn làm sạch dữ liệu cho {editor_name}?"

    print(f"\n⚠️ CẢNH BÁO: {confirm_msg}")
    print("Thao tác này sẽ:")
    print("  • Thay đổi Machine ID và Device ID")
    print("  • Xóa dữ liệu liên quan đến AugmentCode")
    print("  • Tạo backup trước khi thay đổi")

    confirm = input("\n❓ Tiếp tục? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ Đã hủy thao tác.")
        return

    # Execute cleaning
    success_count = 0
    total_count = 0

    if selected_editor == "both":
        available_editors = get_available_editors()
        for editor in available_editors:
            total_count += 1
            if clean_editor_data(editor):
                success_count += 1
    else:
        total_count = 1
        if clean_editor_data(selected_editor):
            success_count = 1

    # Final summary
    print("\n" + "="*60)
    print("📊 KẾT QUẢ CUỐI CÙNG:")
    print(f"✅ Thành công: {success_count}/{total_count} editor(s)")

    if success_count == total_count:
        print("🎉 Tất cả editor đã được làm sạch thành công!")
        print("💡 Bạn có thể khởi động editor và đăng nhập với tài khoản mới.")
    else:
        print("⚠️ Một số editor không thể làm sạch. Kiểm tra lỗi ở trên.")

    print("="*60)


if __name__ == "__main__":
    main()