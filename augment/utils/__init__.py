from .paths import (
    get_home_dir, get_app_data_dir, get_storage_path, get_db_path, get_machine_id_path, get_workspace_storage_path,
    get_cursor_storage_path, get_cursor_db_path, get_cursor_machine_id_path, get_cursor_workspace_storage_path,
    get_editor_storage_path, get_editor_db_path, get_editor_machine_id_path, get_editor_workspace_storage_path
)
from .device_codes import generate_machine_id, generate_device_id
from .editor_detector import detect_installed_editors, get_available_editors, print_editor_status, validate_editor_choice

__all__ = [
    # Original VS Code functions
    'get_home_dir',
    'get_app_data_dir',
    'get_storage_path',
    'get_db_path',
    'get_machine_id_path',
    'get_workspace_storage_path',

    # Cursor AI specific functions
    'get_cursor_storage_path',
    'get_cursor_db_path',
    'get_cursor_machine_id_path',
    'get_cursor_workspace_storage_path',

    # Generic editor functions
    'get_editor_storage_path',
    'get_editor_db_path',
    'get_editor_machine_id_path',
    'get_editor_workspace_storage_path',

    # Device code generation
    'generate_machine_id',
    'generate_device_id',

    # Editor detection
    'detect_installed_editors',
    'get_available_editors',
    'print_editor_status',
    'validate_editor_choice'
]