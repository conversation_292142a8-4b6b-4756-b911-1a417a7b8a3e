import os
from typing import List, Dict
from .paths import (
    get_storage_path, get_cursor_storage_path,
    get_db_path, get_cursor_db_path,
    get_machine_id_path, get_cursor_machine_id_path,
    get_workspace_storage_path, get_cursor_workspace_storage_path
)


def detect_installed_editors() -> Dict[str, Dict[str, bool]]:
    """
    Detect which editors are installed by checking for their configuration files.
    
    Returns:
        dict: Dictionary containing editor detection results
        {
            'vscode': {
                'installed': bool,
                'storage_exists': bool,
                'db_exists': bool,
                'machine_id_exists': bool,
                'workspace_exists': bool
            },
            'cursor': {
                'installed': bool,
                'storage_exists': bool,
                'db_exists': bool,
                'machine_id_exists': bool,
                'workspace_exists': bool
            }
        }
    """
    result = {}
    
    # Check VS Code
    vscode_storage = get_storage_path()
    vscode_db = get_db_path()
    vscode_machine_id = get_machine_id_path()
    vscode_workspace = get_workspace_storage_path()
    
    vscode_storage_exists = os.path.exists(vscode_storage)
    vscode_db_exists = os.path.exists(vscode_db)
    vscode_machine_id_exists = os.path.exists(vscode_machine_id)
    vscode_workspace_exists = os.path.exists(vscode_workspace)
    
    result['vscode'] = {
        'installed': any([vscode_storage_exists, vscode_db_exists, vscode_machine_id_exists, vscode_workspace_exists]),
        'storage_exists': vscode_storage_exists,
        'db_exists': vscode_db_exists,
        'machine_id_exists': vscode_machine_id_exists,
        'workspace_exists': vscode_workspace_exists
    }
    
    # Check Cursor AI
    cursor_storage = get_cursor_storage_path()
    cursor_db = get_cursor_db_path()
    cursor_machine_id = get_cursor_machine_id_path()
    cursor_workspace = get_cursor_workspace_storage_path()
    
    cursor_storage_exists = os.path.exists(cursor_storage)
    cursor_db_exists = os.path.exists(cursor_db)
    cursor_machine_id_exists = os.path.exists(cursor_machine_id)
    cursor_workspace_exists = os.path.exists(cursor_workspace)
    
    result['cursor'] = {
        'installed': any([cursor_storage_exists, cursor_db_exists, cursor_machine_id_exists, cursor_workspace_exists]),
        'storage_exists': cursor_storage_exists,
        'db_exists': cursor_db_exists,
        'machine_id_exists': cursor_machine_id_exists,
        'workspace_exists': cursor_workspace_exists
    }
    
    return result


def get_available_editors() -> List[str]:
    """
    Get list of available editors that have been detected.
    
    Returns:
        list: List of editor names that are installed
    """
    detection_result = detect_installed_editors()
    available = []
    
    if detection_result['vscode']['installed']:
        available.append('vscode')
    
    if detection_result['cursor']['installed']:
        available.append('cursor')
    
    return available


def print_editor_status():
    """
    Print detailed status of detected editors.
    """
    detection_result = detect_installed_editors()
    
    print("=== EDITOR DETECTION RESULTS ===")
    
    for editor_name, editor_info in detection_result.items():
        editor_display = "VS Code" if editor_name == "vscode" else "Cursor AI"
        status = "✅ INSTALLED" if editor_info['installed'] else "❌ NOT FOUND"
        
        print(f"\n{editor_display}: {status}")
        
        if editor_info['installed']:
            print(f"  📄 Storage file: {'✅' if editor_info['storage_exists'] else '❌'}")
            print(f"  🗃️ Database file: {'✅' if editor_info['db_exists'] else '❌'}")
            print(f"  🔑 Machine ID file: {'✅' if editor_info['machine_id_exists'] else '❌'}")
            print(f"  📁 Workspace storage: {'✅' if editor_info['workspace_exists'] else '❌'}")


def validate_editor_choice(editor: str) -> bool:
    """
    Validate if the chosen editor is available.
    
    Args:
        editor (str): Editor name to validate
        
    Returns:
        bool: True if editor is available, False otherwise
    """
    available_editors = get_available_editors()
    return editor.lower() in available_editors


if __name__ == "__main__":
    print_editor_status()
    print(f"\nAvailable editors: {get_available_editors()}")
