# Free AugmentCode - Multi-Editor Support

[![GitHub Repository](https://img.shields.io/badge/GitHub-augment-blue?style=flat-square&logo=github)](https://github.com/huydepzai121/augment)
[![Python Version](https://img.shields.io/badge/Python-3.10+-green?style=flat-square&logo=python)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=flat-square)](LICENSE)
[![VS Code](https://img.shields.io/badge/VS%20Code-Supported-blue?style=flat-square&logo=visual-studio-code)](https://code.visualstudio.com/)
[![Cursor AI](https://img.shields.io/badge/Cursor%20AI-Supported-purple?style=flat-square)](https://cursor.sh/)

Free AugmentCode là một công cụ mạnh mẽ để làm sạch dữ liệu liên quan đến AugmentCode, hỗ trợ cả **VS Code** và **Cursor AI**. Cho phép bạn đăng nhập nhiều tài khoản khác nhau trên cùng một máy tính mà không bị khóa tài khoản.

> 🌟 **Repository chính thức**: https://github.com/huydepzai121/augment

## 🎯 Tính năng chính

### 🔄 **Hỗ trợ đa editor**
- ✅ **VS Code** - Hỗ trợ đầy đủ
- ✅ **Cursor AI** - Hỗ trợ đầy đủ
- 🎛️ **Menu tương tác** - Chọn editor cần làm sạch
- 🔍 **Tự động phát hiện** - Phát hiện editor nào được cài đặt

### 📝 **Thay đổi Telemetry ID**
- 🔄 Reset Device ID và Machine ID
- 💾 Tự động backup dữ liệu gốc
- 🎲 Tạo ID ngẫu nhiên an toàn (cryptographically secure)

### 🗃️ **Làm sạch Database**
- 🧹 Làm sạch SQLite database của editor
- 📦 Tự động backup database trước khi thay đổi
- 🗑️ Xóa các bản ghi chứa từ khóa 'augment'

### 💾 **Quản lý Workspace Storage**
- 📁 Làm sạch workspace storage files
- 🗜️ Tạo backup dạng ZIP
- 🔒 Xử lý file read-only và permission issues

## 📥 Cài đặt

### **Yêu cầu hệ thống:**
- 🐍 **Python 3.10+**
- 💻 **VS Code** và/hoặc **Cursor AI** đã được cài đặt
- 🔧 **AugmentCode extension** đã được cài đặt trong editor

### **Cài đặt:**
1. Clone repository về máy:
   ```bash
   git clone https://github.com/huydepzai121/augment.git
   cd augment
   ```

2. Không cần cài đặt dependencies (chỉ sử dụng Python standard library)

## 🚀 Cách sử dụng

### **Quy trình cơ bản:**

1. **🛑 Thoát hoàn toàn editor:**
   - Thoát AugmentCode extension
   - Đóng hoàn toàn VS Code/Cursor AI
   - Đảm bảo không có process nào đang chạy

2. **▶️ Chạy script:**
   ```bash
   python index.py
   ```

3. **🎯 Chọn editor trong menu:**
   - Script sẽ tự động phát hiện editor được cài đặt
   - Chọn VS Code, Cursor AI, hoặc làm sạch cả hai
   - Xác nhận thao tác

4. **🔄 Khởi động lại editor:**
   - Mở VS Code/Cursor AI
   - Đăng nhập AugmentCode với **email mới**

### **Ví dụ sử dụng:**
```bash
# Chạy script
python index.py

# Menu sẽ hiển thị:
# 1. VS Code
# 2. Cursor AI
# 3. Làm sạch cả hai
# 0. Thoát

# Chọn option và xác nhận
```

## 📁 Cấu trúc dự án

```
free-augmentcode/
├── index.py                    # 🚀 Chương trình chính với menu tương tác
├── augutils/                   # 🔧 Thư mục công cụ chính
│   ├── json_modifier.py            # 📝 Công cụ sửa đổi JSON (hỗ trợ đa editor)
│   ├── sqlite_modifier.py          # 🗃️ Công cụ sửa đổi SQLite database
│   └── workspace_cleaner.py        # 🧹 Công cụ làm sạch workspace
└── utils/                      # 🛠️ Thư mục tiện ích
    ├── paths.py                    # 📍 Quản lý đường dẫn (VS Code + Cursor)
    ├── device_codes.py             # 🎲 Tạo ID ngẫu nhiên
    └── editor_detector.py          # 🔍 Phát hiện editor được cài đặt
```

## 🗂️ Thư mục lưu trữ theo hệ điều hành

### **Windows:**
```
VS Code:     %APPDATA%\Code\User\
Cursor AI:   %APPDATA%\Cursor\User\
```

### **macOS:**
```
VS Code:     ~/Library/Application Support/Code/User/
Cursor AI:   ~/Library/Application Support/Cursor/User/
```

### **Linux:**
```
VS Code:     ~/.config/Code/User/
Cursor AI:   ~/.config/Cursor/User/
```

## 🔒 Tính năng bảo mật

- ✅ **Tự động backup** - Tất cả file gốc được backup với timestamp
- 🔐 **Cryptographically secure** - Sử dụng `secrets` module cho ID generation
- 🌍 **Cross-platform** - Hỗ trợ Windows, macOS, Linux
- 🛡️ **Safe operations** - Kiểm tra file tồn tại trước khi thao tác

## 🤝 Đóng góp

Chào mừng bạn đóng góp cho dự án thông qua:
- 🐛 Báo cáo bug qua Issues
- 💡 Đề xuất tính năng mới
- 🔧 Gửi Pull Request để cải thiện code
- 📖 Cải thiện documentation

## 📄 Giấy phép

Dự án này sử dụng giấy phép **MIT License**. Xem chi tiết tại [LICENSE](LICENSE).