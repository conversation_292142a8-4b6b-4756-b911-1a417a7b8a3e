{"buildCommand": "cd packages/web && bun run build", "devCommand": "cd packages/web && bun run dev", "installCommand": "bun install", "outputDirectory": "packages/web/dist", "framework": "vite", "env": {"NODE_ENV": "production"}, "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/(.*)", "destination": "/index.html"}]}