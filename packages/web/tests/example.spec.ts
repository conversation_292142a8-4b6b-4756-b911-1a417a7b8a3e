import { test, expect } from '@playwright/test';

test('basic playwright setup test', async ({ page }) => {
  // Test cơ bản để kiểm tra Playwright hoạt động
  await page.goto('https://example.com');

  // Kiểm tra title
  await expect(page).toHaveTitle(/Example Domain/);

  // Kiểm tra có heading
  const heading = page.locator('h1');
  await expect(heading).toBeVisible();
  await expect(heading).toHaveText('Example Domain');
});
