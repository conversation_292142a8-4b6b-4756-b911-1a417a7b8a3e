// Mã Google Apps Script đã được sửa đổi để hoạt động tốt hơn

// Hàm doGet để kiểm tra và khởi tạo
function doGet() {
  return ContentService.createTextOutput(
    JSON.stringify({
      success: true,
      message: 'Google Sheets API đang hoạt động!'
    })
  ).setMimeType(ContentService.MimeType.JSON);
}

// Hàm doPost để nhận dữ liệu từ ứng dụng web (hỗ trợ nhiều phương thức)
function doPost(e) {
  try {
    // Log request để debug
    Logger.log('Nhận request:');
    Logger.log(JSON.stringify(e));
    
    let data;
    
    // Kiểm tra xem có phải JSONP hoặc form submit không
    if (e.parameter && e.parameter.data) {
      // Form submit hoặc JSONP
      data = JSON.parse(e.parameter.data);
      Logger.log('Đ<PERSON> nhận dữ liệu từ form/JSONP: ' + e.parameter.data);
    } else if (e.postData && e.postData.contents) {
      // POST API thông thường
      data = JSON.parse(e.postData.contents);
      Logger.log('Đã nhận dữ liệu từ POST: ' + e.postData.contents);
    } else {
      // Không có dữ liệu
      return contentResponse({
        success: false,
        message: 'Không nhận được dữ liệu'
      }, e);
    }
    
    // Lấy sheet hoặc tạo mới
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName('Công việc');
    
    // Nếu không có sheet này, tạo mới
    if (!sheet) {
      sheet = setupTaskSheet();
    }
    
    // Thêm dữ liệu mới vào sheet
    const newRow = [
      data.title || '',
      data.description || '',
      data.type || '',
      data.status || '',
      data.date || '',
      data.time || '',
      data.user_name || '',
      data.assignedTo || '',
      data.team_id || '',
      data.location || '',
      data.created_at || new Date().toISOString()
    ];
    
    // Thêm hàng mới vào sheet
    sheet.appendRow(newRow);
    Logger.log('Đã thêm dữ liệu thành công: ' + JSON.stringify(newRow));
    
    // Trả về response
    return contentResponse({
      success: true,
      message: 'Dữ liệu đã được thêm thành công',
      data: newRow
    }, e);
    
  } catch (error) {
    // Log lỗi
    Logger.log('Lỗi: ' + error.toString());
    
    // Trả về lỗi
    return contentResponse({
      success: false,
      message: error.toString()
    }, e);
  }
}

// Hàm tạo response phù hợp (hỗ trợ cả JSONP và API thông thường)
function contentResponse(data, e) {
  const callback = e.parameter && e.parameter.callback;
  const jsonOutput = JSON.stringify(data);
  
  Logger.log('Đang trả về response: ' + jsonOutput);
  
  if (callback) {
    // JSONP response
    return ContentService.createTextOutput(callback + '(' + jsonOutput + ')')
      .setMimeType(ContentService.MimeType.JAVASCRIPT);
  } else {
    // Thông thường JSON response
    return ContentService.createTextOutput(jsonOutput)
      .setMimeType(ContentService.MimeType.JSON);
  }
}

// Thiết lập Sheet công việc
function setupTaskSheet() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const sheet = ss.insertSheet('Công việc');
  
  // Thêm headers
  const headers = [
    'Tiêu đề', 
    'Mô tả', 
    'Loại công việc', 
    'Trạng thái', 
    'Ngày', 
    'Thời gian', 
    'Người tạo', 
    'Người được giao', 
    'Đội nhóm', 
    'Khu vực', 
    'Ngày tạo'
  ];
  
  // Đặt giá trị cho hàng headers
  sheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  
  // Định dạng header
  const headerRange = sheet.getRange(1, 1, 1, headers.length);
  headerRange.setBackground('#4ecdc4');
  headerRange.setFontColor('#ffffff');
  headerRange.setFontWeight('bold');
  
  // Tạo bộ lọc
  headerRange.createFilter();
  
  // Đặt độ rộng cột
  sheet.setColumnWidth(1, 200); // Tiêu đề
  sheet.setColumnWidth(2, 300); // Mô tả
  sheet.setColumnWidth(3, 150); // Loại công việc
  sheet.setColumnWidth(4, 150); // Trạng thái
  sheet.setColumnWidth(5, 100); // Ngày
  sheet.setColumnWidth(6, 100); // Thời gian
  sheet.setColumnWidth(7, 150); // Người tạo
  sheet.setColumnWidth(8, 150); // Người được giao
  sheet.setColumnWidth(9, 150); // Đội nhóm
  sheet.setColumnWidth(10, 120); // Khu vực
  sheet.setColumnWidth(11, 180); // Ngày tạo
  
  Logger.log('Đã tạo sheet Công việc thành công');
  return sheet;
}
