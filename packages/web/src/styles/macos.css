/* macOS Style CSS */

:root {
  /* macOS Colors */
  --macos-blue: #0078d4;
  --macos-bg-light: rgb(246, 246, 246);
  --macos-fg-light: rgb(29, 29, 31);
  --macos-bg-dark: rgb(30, 30, 32);
  --macos-fg-dark: rgb(250, 250, 250);
  --macos-border-light: rgba(0, 0, 0, 0.1);
  --macos-border-dark: rgba(255, 255, 255, 0.1);
  --macos-radius: 12px;
  --macos-shadow-light: 0 0 10px rgba(0, 0, 0, 0.05);
  --macos-shadow-dark: 0 0 10px rgba(0, 0, 0, 0.2);

  /* Animation Timings */
  --transition-fast: 100ms;
  --transition-medium: 200ms;
  --transition-slow: 300ms;
}

/* macOS Button */
.macos-btn {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
  backdrop-filter: blur(10px);
  border: 1px solid var(--macos-border-light);
  border-radius: var(--macos-radius);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-medium) ease;
  font-weight: 500;
}

.macos-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.macos-btn:active {
  transform: translateY(0);
  opacity: 0.9;
}

/* macOS Card */
.macos-card {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(30px);
  border-radius: var(--macos-radius);
  border: 1px solid var(--macos-border-light);
  box-shadow: var(--macos-shadow-light);
  transition: all var(--transition-medium) ease;
}

.macos-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* macOS Input */
.macos-input {
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid var(--macos-border-light);
  border-radius: var(--macos-radius);
  padding: 8px 12px;
  font-size: 14px;
  transition: all var(--transition-fast) ease;
}

.macos-input:focus {
  border-color: var(--macos-blue);
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
  outline: none;
}

/* macOS Window */
.macos-window {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30px);
  border-radius: var(--macos-radius);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--macos-border-light);
  overflow: hidden;
}

/* macOS Header */
.macos-header {
  height: 32px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--macos-border-light);
  background-color: rgba(246, 246, 246, 0.9);
  backdrop-filter: blur(10px);
}

/* macOS Navigation */
.macos-nav {
  background-color: rgba(246, 246, 246, 0.8);
  backdrop-filter: blur(30px);
  border-right: 1px solid var(--macos-border-light);
}

/* macOS Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* macOS Glass Effect */
.macos-glass {
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid var(--macos-border-light);
  border-radius: var(--macos-radius);
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  .macos-btn {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-color: var(--macos-border-dark);
    color: var(--macos-fg-dark);
  }

  .macos-card {
    background-color: rgba(30, 30, 32, 0.8);
    border-color: var(--macos-border-dark);
    box-shadow: var(--macos-shadow-dark);
    color: var(--macos-fg-dark);
  }

  .macos-input {
    background-color: rgba(50, 50, 55, 0.8);
    border-color: var(--macos-border-dark);
    color: var(--macos-fg-dark);
  }

  .macos-window {
    background-color: rgba(30, 30, 32, 0.9);
    border-color: var(--macos-border-dark);
    color: var(--macos-fg-dark);
  }

  .macos-header {
    background-color: rgba(30, 30, 32, 0.8);
    border-bottom-color: var(--macos-border-dark);
    color: var(--macos-fg-dark);
  }

  .macos-nav {
    background-color: rgba(30, 30, 32, 0.7);
    border-right-color: var(--macos-border-dark);
    color: var(--macos-fg-dark);
  }

  .macos-glass {
    background-color: rgba(30, 30, 32, 0.7);
    border-color: var(--macos-border-dark);
    color: var(--macos-fg-dark);
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}
