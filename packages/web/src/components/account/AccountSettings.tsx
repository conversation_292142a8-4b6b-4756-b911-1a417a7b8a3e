import { User, X } from 'lucide-react';
import React from 'react';

import { useAuth } from '@/context/AuthContext';

interface AccountSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

const AccountSettings: React.FC<AccountSettingsProps> = ({ isOpen, onClose }) => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    console.log('AccountSettings: No current user');
    return null;
  }

  console.log('AccountSettings: Rendering with isOpen:', isOpen, 'currentUser:', currentUser.name);

  if (!isOpen) return null;

  // Removed all complex state for testing

  // Removed password validation for testing

  // Removed handlers for testing

  console.log('AccountSettings: Rendering with isOpen:', isOpen, 'currentUser:', currentUser.name);

  // Temporary simple modal for testing
  if (isOpen) {
    return (
      <div
        className="fixed inset-0 z-[99999] flex items-center justify-center"
        style={{ zIndex: 99999 }}
      >
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />

        {/* Modal Content */}
        <div className="relative bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <User className="w-6 h-6 mr-3" />
                <div>
                  <h2 className="text-xl font-bold">Cài đặt tài khoản</h2>
                  <p className="text-blue-100 text-sm">{currentUser.name}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <p className="text-gray-600">Modal đang hoạt động! Đây là test modal đơn giản.</p>
            <div className="mt-4 space-y-2">
              <p><strong>Tên:</strong> {currentUser.name}</p>
              <p><strong>Email:</strong> {currentUser.email}</p>
              <p><strong>Vai trò:</strong> {currentUser.role}</p>
            </div>
            <div className="mt-6 flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default AccountSettings;
