import React, { useState, useEffect } from 'react';
import { Plus, X, Briefcase, <PERSON>Pen, FileText, Users, Calendar, Clock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/context/AuthContext';
import { useTaskData } from '@/hooks/use-task-data';
import { useToast } from '@/hooks/use-toast';
import { canAssignTasks } from '@/config/permissions';

interface TaskFormData {
  title: string;
  description: string;
  type: string;
  status: string;
  priority: string;
  date: string;
  time?: string;
  assignedTo?: string;
  isShared?: boolean;
  isSharedWithTeam?: boolean;
}

interface TaskFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTaskCreated?: () => void;
  formType: 'self' | 'team' | 'individual';
}

const TaskFormDialog: React.FC<TaskFormDialogProps> = ({
  open,
  onOpenChange,
  onTaskCreated,
  formType,
}) => {
  const { currentUser, users } = useAuth();
  const { addTask } = useTaskData();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    type: '',
    status: 'todo',
    priority: 'normal',
    date: '',
    time: '',
    assignedTo: currentUser?.id || '',
    isShared: false,
    isSharedWithTeam: false,
  });

  const canAssignToOthers = currentUser && canAssignTasks(currentUser.role);

  const filteredUsers = users.filter((user) => {
    if (formType === 'self') return user.id === currentUser?.id;
    if (formType === 'individual') {
      return currentUser?.team_id === user.team_id;
    }
    return true;
  });

  useEffect(() => {
    if (open) {
      setFormData({
        title: '',
        description: '',
        type: '',
        status: 'todo',
        priority: 'normal',
        date: '',
        time: '',
        assignedTo: currentUser?.id || '',
        isShared: false,
        isSharedWithTeam: false,
      });
    }
  }, [open, currentUser]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim() || !formData.description.trim() || !formData.type || !formData.date) {
      toast({
        title: 'Lỗi',
        description: 'Vui lòng điền đầy đủ thông tin bắt buộc',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Tạo task mới
      await addTask({
        title: formData.title,
        description: formData.description,
        type: formData.type,
        status: formData.status as any,
        date: formData.date,
        time: formData.time,
        assignedTo: formData.assignedTo,
        isShared: formData.isShared,
        isSharedWithTeam: formData.isSharedWithTeam,
        priority: formData.priority,
      });

      toast({
        title: 'Thành công',
        description: 'Đã tạo công việc mới thành công',
      });

      // Gọi callback để refresh data
      onTaskCreated?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error submitting task:', error);
      toast({
        title: 'Lỗi',
        description: error instanceof Error ? error.message : 'Không thể tạo công việc mới',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof TaskFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] md:max-w-[900px] lg:max-w-[1000px] max-h-[95vh] overflow-y-auto bg-white/98 backdrop-blur-xl border-0 shadow-2xl rounded-3xl">
        <DialogHeader className="pb-4 border-b border-gray-100/80 bg-gradient-to-r from-blue-50/80 via-indigo-50/80 to-purple-50/80 -m-6 mb-0 p-6 rounded-t-3xl">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
              <Plus className="w-5 h-5 text-white" />
            </div>
            <div>
              <DialogTitle className="text-xl font-bold text-gray-900 tracking-tight">
                {formType === 'self' && 'Tạo công việc mới'}
                {formType === 'team' && 'Giao công việc cho Nhóm'}
                {formType === 'individual' && 'Giao công việc cho thành viên'}
              </DialogTitle>
              <DialogDescription className="text-gray-600 text-sm mt-0.5">
                {formType === 'self' && 'Tạo công việc cá nhân và quản lý tiến độ'}
                {formType === 'team' && 'Phân công công việc cho nhóm hoặc cá nhân bất kỳ'}
                {formType === 'individual' && 'Phân công công việc cho các thành viên trong nhóm'}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 mt-3 p-5">
          {/* Tiêu đề và Mô tả */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div className="bg-white/80 rounded-xl p-4 border border-gray-100/80 shadow-sm backdrop-blur-sm">
              <label className="text-gray-900 font-medium text-sm flex items-center space-x-2 mb-3">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                <span>Tiêu đề công việc</span>
                <span className="text-red-500">*</span>
              </label>
              <Input
                placeholder="Nhập tiêu đề công việc..."
                className="h-10 bg-white/90 border border-gray-200/80 rounded-lg focus:border-blue-400 focus:ring-2 focus:ring-blue-100 text-sm font-medium placeholder-gray-400 shadow-sm"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
              />
            </div>

            <div className="bg-white/80 rounded-xl p-4 border border-gray-100/80 shadow-sm backdrop-blur-sm">
              <label className="text-gray-900 font-medium text-sm flex items-center space-x-2 mb-3">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                <span>Mô tả chi tiết</span>
                <span className="text-red-500">*</span>
              </label>
              <Textarea
                placeholder="Mô tả chi tiết về công việc, yêu cầu, mục tiêu..."
                className="min-h-[80px] bg-white/90 border border-gray-200/80 rounded-lg focus:border-blue-400 focus:ring-2 focus:ring-blue-100 resize-none text-sm text-gray-700 placeholder-gray-400 shadow-sm"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </div>
          </div>

          {/* Loại công việc với card design */}
          <div className="bg-white/80 rounded-xl p-4 border border-gray-100/80 shadow-sm backdrop-blur-sm">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
              <h3 className="text-gray-900 font-medium text-sm">Loại công việc</h3>
              <span className="text-red-500">*</span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-3">
              {[
                { value: 'architect_new', label: 'KTS mới', color: 'from-blue-500 to-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' },
                { value: 'architect_old', label: 'KTS cũ', color: 'from-blue-400 to-blue-500', bg: 'bg-blue-50', border: 'border-blue-200' },
                { value: 'client_new', label: 'KH/CĐT mới', color: 'from-green-500 to-green-600', bg: 'bg-green-50', border: 'border-green-200' },
                { value: 'client_old', label: 'KH/CĐT cũ', color: 'from-green-400 to-green-500', bg: 'bg-green-50', border: 'border-green-200' },
                { value: 'quote_new', label: 'SBG mới', color: 'from-purple-500 to-purple-600', bg: 'bg-purple-50', border: 'border-purple-200' },
                { value: 'quote_old', label: 'SBG cũ', color: 'from-purple-400 to-purple-500', bg: 'bg-purple-50', border: 'border-purple-200' },
                { value: 'partner_new', label: 'Đối tác mới', color: 'from-orange-500 to-orange-600', bg: 'bg-orange-50', border: 'border-orange-200' },
                { value: 'partner_old', label: 'Đối tác cũ', color: 'from-orange-400 to-orange-500', bg: 'bg-orange-50', border: 'border-orange-200' },
                { value: 'other', label: 'Công việc khác', color: 'from-gray-500 to-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' },
              ].map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => handleInputChange('type', type.value)}
                  className={`p-3 rounded-lg border-2 transition-all duration-200 text-sm font-medium text-center ${
                    formData.type === type.value
                      ? `${type.bg} ${type.border} shadow-md scale-105`
                      : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                  }`}
                >
                  <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${type.color} mx-auto mb-2`}></div>
                  <div className={`text-xs ${formData.type === type.value ? 'text-gray-800' : 'text-gray-600'}`}>
                    {type.label}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Thông tin trạng thái và ưu tiên */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white/80 rounded-xl p-4 border border-gray-100/80 shadow-sm backdrop-blur-sm">
              <label className="text-gray-700 font-medium text-sm mb-3 block flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full"></div>
                <span>Trạng thái</span>
              </label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger className="h-10 bg-white/90 border border-gray-200/80 rounded-lg focus:border-blue-400 focus:ring-2 focus:ring-blue-100 hover:border-gray-300 transition-all duration-200 shadow-sm">
                  <SelectValue placeholder="Chọn trạng thái" />
                </SelectTrigger>
                <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-xl overflow-hidden">
                  <SelectItem value="todo" className="py-2.5 px-3 hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
                        Chưa bắt đầu
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="in-progress" className="py-2.5 px-3 hover:bg-blue-50">
                    <div className="flex items-center space-x-3">
                      <div className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
                        Đang thực hiện
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="on-hold" className="py-2.5 px-3 hover:bg-yellow-50">
                    <div className="flex items-center space-x-3">
                      <div className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">
                        Tạm hoãn
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="completed" className="py-2.5 px-3 hover:bg-green-50">
                    <div className="flex items-center space-x-3">
                      <div className="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                        Hoàn thành
                      </div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="bg-white/80 rounded-xl p-4 border border-gray-100/80 shadow-sm backdrop-blur-sm">
              <label className="text-gray-700 font-medium text-sm mb-3 block flex items-center space-x-2">
                <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                <span>Mức độ ưu tiên</span>
              </label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: 'low', label: 'Thấp', color: 'from-green-400 to-green-500', bg: 'bg-green-50', border: 'border-green-200' },
                  { value: 'normal', label: 'Bình thường', color: 'from-yellow-400 to-yellow-500', bg: 'bg-yellow-50', border: 'border-yellow-200' },
                  { value: 'high', label: 'Cao', color: 'from-red-400 to-red-500', bg: 'bg-red-50', border: 'border-red-200' },
                ].map((priority) => (
                  <button
                    key={priority.value}
                    type="button"
                    onClick={() => handleInputChange('priority', priority.value)}
                    className={`p-2.5 rounded-lg border-2 transition-all duration-200 text-xs font-medium text-center ${
                      formData.priority === priority.value
                        ? `${priority.bg} ${priority.border} shadow-md scale-105`
                        : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                    }`}
                  >
                    <div className={`w-2.5 h-2.5 rounded-full bg-gradient-to-r ${priority.color} mx-auto mb-1.5`}></div>
                    <div className={`${formData.priority === priority.value ? 'text-gray-800' : 'text-gray-600'}`}>
                      {priority.label}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Thời gian và Phân công */}
          <div className="bg-white/80 rounded-xl p-4 border border-gray-100/80 shadow-sm backdrop-blur-sm">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full"></div>
              <h3 className="text-gray-900 font-medium text-sm">Thời gian & Phân công</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-gray-700 font-medium text-sm mb-2 block flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-indigo-500" />
                  <span>Ngày thực hiện</span>
                  <span className="text-red-500">*</span>
                </label>
                <Input
                  type="date"
                  className="h-10 bg-white/90 border border-gray-200/80 rounded-lg focus:border-blue-400 focus:ring-2 focus:ring-blue-100 shadow-sm"
                  value={formData.date}
                  onChange={(e) => handleInputChange('date', e.target.value)}
                />
              </div>

              <div>
                <label className="text-gray-700 font-medium text-sm mb-2 block flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-indigo-500" />
                  <span>Thời gian (tùy chọn)</span>
                </label>
                <Input
                  type="time"
                  className="h-10 bg-white/90 border border-gray-200/80 rounded-lg focus:border-blue-400 focus:ring-2 focus:ring-blue-100 shadow-sm"
                  value={formData.time}
                  onChange={(e) => handleInputChange('time', e.target.value)}
                />
              </div>
            </div>

            {(formType === 'team' || formType === 'individual') && canAssignToOthers && (
              <div className="mt-4">
                <label className="text-gray-700 font-medium text-sm mb-2 block flex items-center space-x-2">
                  <Users className="w-4 h-4 text-indigo-500" />
                  <span>Giao cho</span>
                </label>
                <Select value={formData.assignedTo} onValueChange={(value) => handleInputChange('assignedTo', value)}>
                  <SelectTrigger className="h-10 bg-white/90 border border-gray-200/80 rounded-lg focus:border-blue-400 focus:ring-2 focus:ring-blue-100 shadow-sm">
                    <SelectValue placeholder="Chọn người thực hiện" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border border-gray-200 shadow-lg rounded-xl overflow-hidden">
                    {filteredUsers.map((user) => (
                      <SelectItem key={user.id} value={user.id} className="py-2 px-3 hover:bg-gray-50">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>{user.name}</span>
                          <span className="text-xs text-gray-500">- {user.location || 'Chưa xác định'}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {formType === 'team' && (
              <div className="mt-4 p-3 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 rounded-lg border border-blue-100/80 backdrop-blur-sm">
                <div className="space-y-2">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="shareWithAll"
                      checked={formData.isShared || false}
                      onChange={(e) => handleInputChange('isShared', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-white border-2 border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                    />
                    <label htmlFor="shareWithAll" className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                      <span>Chia sẻ với tất cả nhân viên</span>
                    </label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="shareWithTeam"
                      checked={formData.isSharedWithTeam || false}
                      onChange={(e) => handleInputChange('isSharedWithTeam', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-white border-2 border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                    />
                    <label htmlFor="shareWithTeam" className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                      <span>Chia sẻ với nhóm của tôi</span>
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter className="mt-5 p-4 bg-gradient-to-r from-gray-50/80 to-blue-50/80 -m-5 mt-5 rounded-b-3xl border-t border-gray-100/80 backdrop-blur-sm">
            <div className="flex flex-col sm:flex-row gap-3 w-full">
              <Button
                type="button"
                variant="outline"
                className="h-10 px-6 rounded-lg border border-gray-200/80 hover:bg-white hover:border-gray-300 hover:shadow-sm transition-all duration-200 font-medium text-gray-700 bg-white/80"
                onClick={() => onOpenChange(false)}
              >
                <X className="w-4 h-4 mr-2" />
                Hủy bỏ
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !formData.title.trim() || !formData.description.trim() || !formData.type || !formData.date}
                className="h-10 px-6 rounded-lg bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium shadow-md hover:shadow-lg transition-all duration-200 flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Đang lưu...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    {formType === 'self'
                      ? 'Tạo công việc'
                      : formType === 'team'
                        ? 'Giao công việc cho Nhóm'
                        : 'Giao công việc cho thành viên'}
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default TaskFormDialog;
