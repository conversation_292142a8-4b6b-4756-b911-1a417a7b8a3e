{"fieldOverrides": [], "indexes": [{"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "team_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "location", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "team_id", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}]}]}