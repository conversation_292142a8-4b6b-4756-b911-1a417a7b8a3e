rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Rules for users collection - Allow read/write for development
    match /users/{userId} {
      allow read, write: if true;
    }

    // Rules for teams collection - Allow read/write for development
    match /teams/{teamId} {
      allow read, write: if true;
    }

    // Rules for tasks collection - Allow read/write for development
    match /tasks/{taskId} {
      allow read, write, delete: if true;
    }

    // Rules for reports collection - Allow read/write for development
    match /reports/{reportId} {
      allow read, write: if true;
    }

    // Rules for settings collection - Allow read/write for development
    match /settings/{settingId} {
      allow read, write: if true;
    }

    // Rules for notifications collection - Allow read/write for development
    match /notifications/{notificationId} {
      allow read, write: if true;
    }
  }
}
