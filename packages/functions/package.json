{"name": "functions", "private": true, "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "deploy": "firebase deploy --only functions", "lint": "eslint --ext .js,.ts .", "logs": "firebase functions:log", "serve": "npm run build && firebase emulators:start --only functions,firestore,auth", "serve:functions-only": "npm run build && firebase emulators:start --only functions", "serve:all": "npm run build && firebase emulators:start", "serve:auth": "npm run build && firebase emulators:start --only auth", "serve:watch": "npm run build && firebase emulators:start --only functions,firestore,auth --inspect-functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell"}, "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "firebase-admin": "^12.1.0", "firebase-functions": "^6.3.2"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "engines": {"node": "18"}}