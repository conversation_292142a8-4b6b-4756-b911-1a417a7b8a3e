
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 212 100% 52%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 212 100% 52%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sf-pro;
    font-feature-settings: "ss01", "ss02", "ss03";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* SF Pro Fonts */
@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-regular-webfont.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-medium-webfont.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-semibold-webfont.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Display';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscodisplay-bold-webfont.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscotext-regular-webfont.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'SF Pro Text';
  src: url('https://applesocial.s3.amazonaws.com/assets/styles/fonts/sanfrancisco/sanfranciscotext-medium-webfont.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

/* macOS-style utility classes */
@layer components {
  .macos-card {
    @apply bg-white/90 backdrop-blur-xl rounded-xl shadow-md border border-white/10 overflow-hidden;
  }
  
  .macos-button {
    @apply bg-ios-blue text-white font-medium rounded-lg px-4 py-2 transition-all hover:bg-ios-blue/90 active:scale-[0.98] shadow-sm;
  }
  
  .macos-button-secondary {
    @apply bg-white/80 text-ios-dark font-medium rounded-lg border border-gray-200/50 px-4 py-2 transition-all hover:bg-white/100 active:scale-[0.98] shadow-sm;
  }
  
  .macos-input {
    @apply bg-white/80 border border-gray-200/50 rounded-lg px-4 py-3 w-full focus:ring-2 focus:ring-ios-blue focus:outline-none transition-all;
  }
  
  .macos-select {
    @apply bg-white/80 border border-gray-200/50 rounded-lg px-4 py-3 w-full focus:ring-2 focus:ring-ios-blue focus:outline-none transition-all;
  }
  
  .blur-background {
    @apply backdrop-blur-xl bg-white/80 dark:bg-[#1c1c1e]/80 border border-white/10 dark:border-white/5;
  }
  
  .ios-card {
    @apply bg-white/90 backdrop-blur-xl rounded-xl shadow-md border border-white/10 overflow-hidden;
  }
  
  .ios-button {
    @apply bg-ios-blue text-white font-medium rounded-lg px-4 py-2 transition-all hover:bg-ios-blue/90 active:scale-[0.98] shadow-sm;
  }
  
  .ios-button-secondary {
    @apply bg-white/80 text-ios-dark font-medium rounded-lg border border-gray-200/50 px-4 py-2 transition-all hover:bg-white/100 active:scale-[0.98] shadow-sm;
  }
  
  .ios-input {
    @apply bg-white/80 border border-gray-200/50 rounded-lg px-4 py-3 w-full focus:ring-2 focus:ring-ios-blue focus:outline-none transition-all;
  }
}

/* Animation for background gradient */
@keyframes gradient-animation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-animation {
  animation: gradient-animation 15s ease infinite;
}

/* Pattern background */
.bg-pattern {
  background-image: radial-gradient(circle, rgba(255,255,255,0.1) 20%, transparent 20%);
  background-size: 30px 30px;
}

/* Float animation */
@keyframes float {
  0% { transform: translate(0, 0); }
  50% { transform: translate(-50px, 50px); }
  100% { transform: translate(0, 0); }
}

.animate-float {
  animation: float 20s linear infinite;
}

/* Shimmer effect */
@keyframes shimmer {
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

/* Modal Styles - Fix positioning issues */
.modal-overlay,
.account-settings-modal {
    position: fixed !important;
    z-index: 99999 !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}

.modal-content,
.account-settings-content {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 100000 !important;
    max-height: 80vh !important;
    max-width: 90vw !important;
    overflow-y: auto !important;
    background: #ffffff !important;
    border-radius: 16px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.modal-backdrop {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background-color: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    z-index: 99998 !important;
}

body.modal-open {
    overflow: hidden !important;
}

.top-navigation,
.bottom-navigation,
.sidebar {
    z-index: 1000 !important;
}

@media (max-width: 768px) {
    .modal-content,
    .account-settings-content {
        width: 95vw !important;
        max-height: 95vh !important;
        margin: 0 !important;
    }

    /* Ensure account settings modal is properly sized on mobile */
    div:has(h2:contains("Cài đặt tài khoản")) {
        max-height: 95vh !important;
        width: 95vw !important;
    }
}

/* Notification Center - Proper positioning */
div[data-notification="center"] {
    z-index: 60 !important;
    position: absolute !important;
}

/* Notification panel styling */
.notification-panel,
.notification-center-panel {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

/* Account Settings Modal - Highest priority */
div[class*="modal"],
div[class*="Modal"],
div[data-modal],
.account-settings-modal-main,
[data-modal="account-settings"] {
    z-index: 99999 !important;
}

/* Advanced Modal Fix - Force positioning */
body > div:has(canvas),
body > div:has(svg),
div:has(> .recharts-wrapper),
.recharts-wrapper,
.recharts-surface,
.recharts-layer,
svg[class*="recharts"],
canvas,
div[class*="chart"],
div[class*="Chart"] {
    position: relative !important;
    z-index: 1 !important;
}

div[style*="position: fixed"],
div[style*="position:fixed"] {
    z-index: 9999 !important;
}

div:has(h2:contains("Cài đặt tài khoản")),
div:has(text:contains("Nguyễn Mạnh Linh")) {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 99999 !important;
    background: white !important;
    border-radius: 16px !important;
    box-shadow: 0 0 100px rgba(0,0,0,0.5) !important;
}

div:has(h2:contains("Cài đặt tài khoản"))::before {
    content: '';
    position: fixed !important;
    inset: 0 !important;
    background: rgba(0,0,0,0.8) !important;
    z-index: -1 !important;
    backdrop-filter: blur(10px) !important;
}

/* Additional specific targeting */
.account-settings-modal-main,
[data-modal="account-settings"] {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 99999 !important;
    background: white !important;
    border-radius: 16px !important;
    box-shadow: 0 0 100px rgba(0,0,0,0.5) !important;
    max-width: 90vw !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
}

/* Account popover positioning - simplified */
[data-radix-popper-content-wrapper] {
    z-index: 1000 !important;
}

/* Ensure popover appears above sidebar */
[data-radix-popover-content] {
    z-index: 1000 !important;
}

/* Prevent sidebar from clipping popover */
.sidebar-desktop {
    overflow: visible !important;
}

/* Ensure popover container is not clipped */
[data-radix-popover-content][data-side="right"] {
    z-index: 1000 !important;
}

/* Sidebar responsive styles */
@media (max-width: 768px) {
    /* Hide sidebar on mobile and show bottom navigation instead */
    .sidebar-desktop {
        display: none !important;
    }

    /* Adjust main content margin on mobile */
    main {
        margin-left: 0 !important;
        padding-bottom: 4rem !important;
    }

    /* Show bottom navigation on mobile */
    .bottom-nav-mobile {
        display: flex !important;
    }
}

@media (min-width: 769px) {
    /* Hide bottom navigation on desktop */
    .bottom-nav-mobile {
        display: none !important;
    }

    /* Ensure main content has proper margin on desktop */
    main {
        margin-left: 16rem !important;
        padding-bottom: 0 !important;
        transition: margin-left 0.3s ease-in-out !important;
    }

    /* Adjust main content when sidebar is collapsed */
    main.sidebar-collapsed {
        margin-left: 4rem !important;
    }
}

/* Sidebar animations */
.sidebar-desktop {
    transition: width 0.3s ease-in-out;
}

/* Smooth transitions for main content */
main {
    transition: margin-left 0.3s ease-in-out, padding-bottom 0.3s ease-in-out;
}

/* Tooltip styles for collapsed sidebar */
.sidebar-desktop .group:hover .absolute {
    z-index: 9999;
}

/* Notification Center - Force highest z-index */
div[data-notification="center"] {
    z-index: 99999 !important;
    position: fixed !important;
}

/* Ensure main content doesn't interfere with notifications */
main,
main > div,
.p-4,
.p-6,
.space-y-8,
.grid,
.flex-1,
.overflow-y-auto {
    z-index: 0 !important;
    position: relative !important;
}

/* Override table sticky headers and cells that might interfere */
table th[style*="position: sticky"],
table td[style*="position: sticky"],
th[style*="zIndex"],
td[style*="zIndex"] {
    z-index: 5 !important;
}

/* Force all tables and their content to have low z-index */
table,
thead,
tbody,
tr,
th,
td {
    z-index: auto !important;
}
